import React from "react";
import ReactDOM from "react-dom";

import {
  InputData,
  CreateSite,
  NginxEdit,
  SSL,
  LnS,
  Database,
  CreateWordpress,
  SecureWordpress,
  SimpleLayout,
  ConfigSummary
} from "../components";

import { AppProvider, useApp } from "../contexts/AppContext";

import "bootstrap/dist/css/bootstrap.min.css";
import {
  Accordion,
} from "react-bootstrap";
import "./sidepanel.css";

const MainContent: React.FC = () => {
  const { state, setActiveSection } = useApp();

  const handleAccordionSelect = (eventKey: string | null) => {
    console.log('Accordion select:', eventKey, 'Current active:', state.activeSection);
    if (eventKey) {
      setActiveSection(eventKey);
    }
  };

  return (
    <div>
      <div className="page-header">
        <h1>🖥️ Ubuntu Webserver Manager</h1>
        <p className="text-muted">Manage your Ubuntu web server from the sidebar</p>
      </div>

      <ConfigSummary />

      <Accordion
        activeKey={state.activeSection}
        onSelect={handleAccordionSelect}
        flush
      >
        <InputData eventKey="0" />
        <CreateSite eventKey="1" />
        <NginxEdit eventKey="2" />
        <SSL eventKey="3" />
        <LnS eventKey="4" />
        <Database eventKey="5" />
        <CreateWordpress eventKey="6" />
        <SecureWordpress eventKey="7" />
      </Accordion>
    </div>
  );
};

const App: React.FC = () => {
  return (
    <AppProvider>
      <SimpleLayout>
        <MainContent />
      </SimpleLayout>
    </AppProvider>
  );
};

const root = document.createElement("div");
document.body.appendChild(root);
ReactDOM.render(<App />, root);
