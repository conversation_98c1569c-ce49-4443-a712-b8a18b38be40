import React, { useState } from "react";
import ReactDOM from "react-dom";

import {
  InputData,
  CreateSite,
  NginxEdit,
  SSL,
  LnS,
  Database,
  CreateWordpress,
  SecureWordpress
} from "../components";

import "bootstrap/dist/css/bootstrap.min.css";
import {
  Container,
  Row,
  Col,
  Accordion,
} from "react-bootstrap";

import "./popup.css";

const App: React.FC<{}> = () => {
  const [domain, setDomain] = useState<string>("example.com");
  const [dbName, setdbName] = useState<string>("db_name");
  // const [dbNewName, setdbNewName] = useState<string>("db_new_name");
  const [dbUser, setdbUser] = useState<string>("db_user");
  const [dbPass, setdbdbPass] = useState<string>("db_pass");

  return (
    <Container>
      <Row>
        <Col>
          <h1>Ubuntu - Webserver</h1>
          <Accordion defaultActiveKey="0">
            <InputData
              eventKey="0"
              setDomain={setDomain}
              setdbName={setdbName}
              setdbUser={setdbUser}
              setdbdbPass={setdbdbPass}
            />
            <CreateSite
              eventKey="1"
              domain={domain}
              dbName={dbName}
              dbUser={dbUser}
              dbPass={dbPass}
            />
            <NginxEdit eventKey="2" domain={domain} />
            <SSL eventKey="3" domain={domain} />
            <LnS eventKey="4" domain={domain} />
            <Database
              eventKey="5"
              dbName={dbName}
              dbUser={dbUser}
              dbPass={dbPass}
            />
            <CreateWordpress
              eventKey="6"
              domain={domain}
              dbName={dbName}
              dbUser={dbUser}
              dbPass={dbPass}
            />
            <SecureWordpress eventKey="7" domain={domain} />
          </Accordion>
        </Col>
      </Row>
    </Container>
  );
};

const root = document.createElement("div");
document.body.appendChild(root);
ReactDOM.render(<App />, root);
