import React from "react";
import {
    Form,
    Accordion,
} from "react-bootstrap";

interface InputDataProp {
    eventKey: string,
    setDomain: React.Dispatch<React.SetStateAction<string>>,
    setdbName: React.Dispatch<React.SetStateAction<string>>,
    setdbUser: React.Dispatch<React.SetStateAction<string>>,
    setdbdbPass: React.Dispatch<React.SetStateAction<string>>,
}

const InputData: React.FC<InputDataProp> = ({ eventKey, setDomain, setdbName, setdbUser, setdbdbPass }) => {
    return (
        <Accordion.Item eventKey={eventKey}>
            <Accordion.Header>1. Input Data</Accordion.Header>
            <Accordion.Body>
                <Form>
                    <Form.Group className="mb-3">
                        <Form.Label>Domain:</Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Enter domain"
                            onChange={(event) => setDomain(event.target.value)}
                        />
                    </Form.Group>
                    <Form.Group className="mb-3">
                        <code>pwgen 10 3 -s</code>
                        <Form.Label>Database Name:</Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Database Name"
                            onChange={(event) => setdbName(event.target.value)}
                        />
                    </Form.Group>
                    <Form.Group className="mb-3">
                        <Form.Label>Database Username:</Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Database Username"
                            onChange={(event) => setdbUser(event.target.value)}
                        />
                    </Form.Group>
                    <Form.Group className="mb-3">
                        <Form.Label>Database Password:</Form.Label>
                        <Form.Control
                            type="text"
                            placeholder="Database Password"
                            onChange={(event) => setdbdbPass(event.target.value)}
                        />
                    </Form.Group>
                </Form>
            </Accordion.Body>
        </Accordion.Item>
    )
}

export default InputData