import React from "react";
import {
    Accordion,
    ListGroup
} from "react-bootstrap";


const NginxEdit: React.FC<{domain: string, eventKey: string}> = ({eventKey, domain}) => {

    return (
        <Accordion.Item eventKey={eventKey}>
            <Accordion.Header>2. Nginx Edit</Accordion.Header>
            <Accordion.Body>
                <h6>cd /etc/nginx/sites-available/</h6>
                <h6>sudo nano {domain}</h6>
                <ListGroup variant="flush" as="ol" numbered>
                    <ListGroup.Item
                        as="li"
                        className="d-flex justify-content-between align-items-start"
                    >
                        <div className="ms-2 me-auto">
                            <div className="fw-bold">
                                Replace Domain in nano editor
                            </div>
                            <code>Ctrl + \</code>
                            <code>{domain}</code>
                        </div>
                    </ListGroup.Item>
                    <ListGroup.Item
                        as="li"
                        className="d-flex justify-content-between align-items-start"
                    >
                        <div className="ms-2 me-auto">
                            <div className="fw-bold">
                                Comment wp_security_directives.conf
                            </div>
                            <code>#</code>
                            <code>{domain}</code>
                        </div>
                    </ListGroup.Item>
                </ListGroup>
            </Accordion.Body>
        </Accordion.Item>
    )
}

export default NginxEdit;