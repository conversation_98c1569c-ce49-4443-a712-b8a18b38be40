import React, {useState} from "react";
import {
    ListGroup,
    Col,
    Form
} from "react-bootstrap";


const ConvertPFXtoCertKey: React.FC = () => {
    const [domain, setDomain] = useState<string>("example.com")
    return (
        <Col>
          <h1>Convert pfx to cert and key</h1>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Domain</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter domain"
                onChange={(event) => setDomain(event.target.value)}
              />
            </Form.Group>
          </Form>
          <ListGroup variant="flush" as="ol" numbered>
            <ListGroup.Item
              as="li"
              className="d-flex justify-content-between align-items-start"
            >
              <div className="ms-2 me-auto">
                <div className="fw-bold">Convert pfx to cert</div>
                <code>
                  pkcs12 -in ./ssl-exported/{domain}.pfx -clcerts -nokeys -out
                  ./certs-keys/{domain}.crt
                </code>
              </div>
            </ListGroup.Item>
            <ListGroup.Item
              as="li"
              className="d-flex justify-content-between align-items-start"
            >
              <div className="ms-2 me-auto">
                <div className="fw-bold">Convert pfx to key</div>
                <code>
                  pkcs12 -in ./ssl-exported/{domain}.pfx -nodes -nocerts -out
                  ./certs-keys/{domain}.key{" "}
                </code>
              </div>
            </ListGroup.Item>
          </ListGroup>
        </Col>
    )
}

export default ConvertPFXtoCertKey;