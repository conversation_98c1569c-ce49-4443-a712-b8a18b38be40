import React from "react";
import {
    Accordion,
    ListGroup
} from "react-bootstrap";

interface CreateWPProp {
  eventKey: string,
  domain: string,
  dbUser: string
  dbName: string,
  dbPass: string
}

const CreateWordpress: React.FC<CreateWPProp> = ({eventKey, domain, dbUser, dbName, dbPass}) => {

    return (
        <Accordion.Item eventKey={eventKey}>
              <Accordion.Header>6. Create Source Wordpress</Accordion.Header>
              <Accordion.Body>
                <ListGroup variant="flush" as="ol" numbered>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Copy source from betheme-vi.com
                      </div>
                      <code>cd /var/www/</code>
                      <code>sudo cp betheme-vi.com {domain}</code>
                      <code>cd {domain}/public_html</code>
                      <code>sudo rm -r wp-config.php</code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Goto {domain} to new install
                      </div>
                      <code>{domain}</code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">Edit wp-config.php file</div>
                      <code>
                        /** Allow Direct Updating Without FTP */
                        <br />
                        define ('FS_METHOD', 'direct');
                        <br />
                        /** Disable Editing of Themes and Plugins Using the
                        Built In Editor */
                        <br />
                        define ('DISALLOW_FILE_EDIT', true);
                        <br />
                        /* That's all, stop editing! Happy publishing. */
                      </code>
                    </div>
                  </ListGroup.Item>

                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">Cài đặt WordPress với WP CLI</div>
                      <b>1. Tải xuống WordPress: </b>
                      <code>wp core download</code>
                      <b>2. Tạo tệp cấu hình wp-config.php: </b>
                      <code>wp config create --dbname={dbName} --dbuser={dbUser} --dbpass={dbPass} --dbhost=localhost --dbprefix=wp_Betheme_</code>
                      <b>3. Chạy lệnh cài đặt WordPress: </b>
                      <code>wp core install --url={domain} --title="Nail Salon - Best Nail Salon in Schenectady" --admin_user=nailAdmin --admin_password=Phuocloc2022! --admin_email=<EMAIL> --skip-email</code>
                    </div>
                  </ListGroup.Item>

                </ListGroup>
              </Accordion.Body>
            </Accordion.Item>
    )
}

export default CreateWordpress;