import React from "react";
import {
    Accordion,
    ListGroup
} from "react-bootstrap";

interface DatabaseProp {
    eventKey: string,
    dbUser: string
    dbName: string,
    dbPass: string
}

const Database: React.FC<DatabaseProp> = ({eventKey, dbUser, dbName, dbPass}) => {

    return (
        <Accordion.Item eventKey={eventKey}>
              <Accordion.Header>5. Database</Accordion.Header>
              <Accordion.Body>
                <ListGroup variant="flush" as="ol" numbered>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">login to MariaDB</div>
                      <code>sudo mysql -u root</code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">Create a databse</div>
                      <code>CREATE DATABASE {dbName};</code>
                      <code>CREATE DATABASE {dbName};</code>
                      <code>
                        CREATE DATABASE {dbName} DEFAULT CHARACTER SET utf8
                        COLLATE utf8_general_ci;
                      </code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Create database user and give the user a password
                      </div>
                      <code>
                        CREATE USER '{dbUser}'@'localhost' IDENTIFIED by '
                        {dbPass}';
                      </code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Give user privileges on a database
                      </div>
                      <code>
                        GRANT ALL PRIVILEGES ON {dbName}.* TO '{dbUser}
                        '@'localhost';
                      </code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">Reloads the grant tables</div>
                      <code>FLUSH PRIVILEGES;</code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Exporting a MySQL or MariaDB Database
                      </div>
                      <code>
                        mysqldump -u {dbUser} -p {dbName} {">"} {dbName}.sql
                      </code>
                      <code>head -n 5 data-dump.sql</code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Importing a MySQL or MariaDB Database
                      </div>
                      <code>
                        mysql -u {dbUser} -p {dbName} {"<"} {dbName}.sql
                      </code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                      DROP TABLES OF A DATABASE WORDPRESS
                      </div>
                      <code>
                      DROP TABLE IF EXISTS `wp_brb_business`, `wp_brb_review`, `wp_commentmeta`, `wp_comments`, `wp_duplicator_pro_entities`, `wp_duplicator_pro_packages`, `wp_fusion_form_entries`, `wp_fusion_form_fields`, `wp_fusion_form_submissions`, `wp_fusion_forms`, `wp_links`, `wp_options`, `wp_postmeta`, `wp_posts`, `wp_term_relationships`, `wp_term_taxonomy`, `wp_termmeta`, `wp_terms`, `wp_usermeta`, `wp_users`, `wp_yoast_indexable`, `wp_yoast_indexable_hierarchy`, `wp_yoast_migrations`, `wp_yoast_primary_term`, `wp_yoast_prominent_words`, `wp_yoast_seo_links`;
                      </code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">More</div>
                      <code>
                        - view privileges for a particular user: show grants for
                        '{dbUser}
                        '@'localhost';
                      </code>
                      <code>- list databases: show databases;</code>
                      <code>
                        - list databases users: select user from mysql.user;
                      </code>
                      <code>- delete a database: drop database {dbName};</code>
                      <code>- delete a database user: drop user {dbUser};</code>
                      <code>- exit mariadb: exit</code>
                    </div>
                  </ListGroup.Item>
                </ListGroup>
              </Accordion.Body>
            </Accordion.Item>
    )
}

export default Database;