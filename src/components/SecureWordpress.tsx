import React from "react";
import {
    Accordion,
    ListGroup
} from "react-bootstrap";


const SecureWordpress: React.FC<{domain: string, eventKey: string}> = ({eventKey, domain}) => {

    return (
        <Accordion.Item eventKey={eventKey}>
              <Accordion.Header>7. Secure Your Wordpress Site</Accordion.Header>
              <Accordion.Body>
                <ListGroup variant="flush" as="ol" numbered>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Implementing Wordpress Ownership
                      </div>
                      <code>
                        sudo chown -R $USER:www-data /var/www/{domain}
                      </code>
                      <code>
                        sudo chown -R www-data:www-data /var/www/{domain}
                        /public_html/wp-content
                      </code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Implementing Wordpress Permissions
                      </div>
                      Chmod all directories: 755
                      <code>
                        sudo find /var/www/{domain}/public_html/ -type d -exec
                        chmod 755 {"{}"} \;
                      </code>
                      Chmod all files: 644
                      <code>
                        sudo find /var/www/{domain}/public_html/ -type f -exec
                        chmod 644 {"{}"} \;
                      </code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Setting Wp-Config.Php Permissions
                      </div>
                      Chmod wp-config.php: 664
                      <code>
                        sudo chmod 664 /var/www/{domain}
                        /public_html/wp-config.php
                      </code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">Uncoment in nginx file</div>
                      uncoment include
                      /etc/nginx/my_includes/wp_security_directives.conf;
                      <code>sudo nano /etc/nginx/sites-available/{domain}</code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">Optimizing Wordpress</div>
                      uncoment include
                      /etc/nginx/my_includes/wp_super_cache.conf; comment
                      location ~ \.php$
                      <code>sudo nano /etc/nginx/sites-available/{domain}</code>
                    </div>
                  </ListGroup.Item>
                </ListGroup>
              </Accordion.Body>
            </Accordion.Item>
    )
}

export default SecureWordpress;