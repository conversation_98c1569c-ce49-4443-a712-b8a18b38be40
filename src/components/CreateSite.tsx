import React, { useState } from "react";
import {
    Form,
    Accordion,
    ListGroup
} from "react-bootstrap";

interface CreateSiteProp {
    eventKey: string,
    domain: string,
    dbUser: string
    dbName: string,
    dbPass: string
}

const CreateSite: React.FC<CreateSiteProp> = ({ eventKey, domain, dbUser, dbName, dbPass }) => {
    const [dbNewName, setdbNewName] = useState<string>("db_new_name");

    return (
        <Accordion.Item eventKey={eventKey}>
              <Accordion.Header>2. Create Site - Nginx</Accordion.Header>
              <Accordion.Body>
                <ListGroup variant="flush" as="ol" numbered>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Add Site - Add SSL Configure - ln -s
                      </div>
                      <code>sudo ./addSite.sh {domain}</code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Move SSL - Permisssion SSL - Change Owner SSL
                      </div>
                      <code>./permisssionSsl.sh {domain}</code>
                      <code>./testRestartNginx.sh</code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Generate Database Info
                      </div>
                      <code>./createDbInfo.sh</code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Import & Export Database
                      </div>
                      <Form>
                        <Form.Group className="mb-3">
                          <Form.Control
                            type="text"
                            placeholder="Database New Name"
                            onChange={(event) => setdbNewName(event.target.value)}
                          />
                        </Form.Group>
                      </Form>
                      <p>1. Import Database
                        <code>./importSql.sh -u {dbUser} -n {dbName} -nn {dbNewName}</code></p>
                      <p>2. Export Database
                        <code>./exportSql.sh -u {dbUser} -n {dbName} -nn {dbNewName}</code></p>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Mysql/ Mariadb - Database
                      </div>
                      <p>1. Login Mysql
                        <code>./loginMysql.sh</code></p>
                      <p>2. Create Database
                        <code>
                          CREATE DATABASE {dbName} DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
                        </code></p>
                      <p>3. Create Database Account
                        <code>
                          CREATE USER '{dbUser}'@'localhost' IDENTIFIED by '
                          {dbPass}';
                        </code></p>
                      <p>4. Privileges on Database
                        <code>
                          GRANT ALL PRIVILEGES ON {dbName}.* TO '{dbUser}
                          '@'localhost';
                        </code></p>
                      <p>5. Refresh Database
                        <code>FLUSH PRIVILEGES;</code></p>
                      <p>6. Exit MariaDB/ Mysql
                        <code>exit;</code></p>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Copy Wordpress Source from Betheme-vi.com
                      </div>
                      <code>./copyBethemeVi.com.sh {domain}</code>
                      <code>./restartPhp.sh</code>
                      <a href={"https://" + domain}>Go to {domain}</a>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Test Nginx - Restart Nginx - Restart Nginx & PHP
                      </div>
                      <code>./testNginx.sh</code>
                      <code>./restartNginx.sh</code>
                      <code>./restartBoth.sh</code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Copy Betheme & Copy Betheme Null
                      </div>
                      <code>./copyThemeBe.sh</code>
                      <code>./copyThemeBeChild.sh</code>
                      <code>./copyThemeBeNull.sh</code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Permisssion Wordpress
                      </div>
                      <code>./permisssionWp.sh {domain}</code>
                    </div>
                  </ListGroup.Item>
                </ListGroup>
              </Accordion.Body>
            </Accordion.Item>
    )
}

export default CreateSite