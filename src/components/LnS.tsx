import React, { useState } from "react";
import {
    Accordion,
    ListGroup,
    Form
} from "react-bootstrap";


const LnS: React.FC<{ domain: string, eventKey: string }> = ({ eventKey, domain }) => {
    const [domainLocal, setDomainLocal] = useState<string>("example.com")
    return (
        <Accordion.Item eventKey={eventKey}>
            <Accordion.Header>4. ln -s</Accordion.Header>

            <Accordion.Body>
                <ListGroup variant="flush" as="ol" numbered>
                    <ListGroup.Item
                        as="li"
                        className="d-flex justify-content-between align-items-start"
                    >
                        <div className="ms-2 me-auto">
                            <div className="fw-bold">ln -s</div>
                            <code>
                                sudo ln -s /etc/nginx/sites-available/{domain}{" "}
                                /etc/nginx/sites-enabled
                            </code>
                        </div>
                    </ListGroup.Item>
                    <ListGroup.Item
                        as="li"
                        className="d-flex justify-content-between align-items-start"
                    >
                        <div className="ms-2 me-auto">
                            <div className="fw-bold">ln -s Betheme theme</div>
                            <Form>
                                <Form.Group className="mb-3">
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter domain"
                                        onChange={(event) => setDomainLocal(event.target.value)}
                                    />
                                </Form.Group>
                            </Form>
                            <code>
                                sudo ln -s /var/www/betheme-vi.com/public_html/wp-content/themes/betheme /var/www/{domainLocal}/public_html/wp-content/themes/
                            </code>
                            <div className="fw-bold">ln -s Avada theme</div>
                            <code>
                                sudo ln -s /var/www/nailstudiospa.net/public_html/wp-content/themes/Avada /var/www/{domainLocal}/public_html/wp-content/themes/
                            </code>
                        </div>
                    </ListGroup.Item>
                    <ListGroup.Item
                        as="li"
                        className="d-flex justify-content-between align-items-start"
                    >
                        <div className="ms-2 me-auto">
                            <div className="fw-bold">Test & Restart Nginx</div>
                            <code>sudo nginx -t</code>
                            <code>sudo systemctl restart nginx</code>
                        </div>
                    </ListGroup.Item>
                </ListGroup>
            </Accordion.Body>
        </Accordion.Item>
    )
}

export default LnS;