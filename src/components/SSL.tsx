import React from "react";
import {
    Accordion,
    ListGroup
} from "react-bootstrap";


const SSL: React.FC<{domain: string, eventKey: string}> = ({eventKey, domain}) => {

    return (
        <Accordion.Item eventKey={eventKey}>
              <Accordion.Header>3. SSL</Accordion.Header>
              <Accordion.Body>
                <ListGroup variant="flush" as="ol" numbered>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">Replace {domain} SSL</div>
                      <code>cd /etc/nginx/snippets</code>
                      <code>sudo cp betheme-vi.conf {domain}.conf</code>
                      <code>sudo nano {domain}.conf</code>
                    </div>
                  </ListGroup.Item>
                  <ListGroup.Item
                    as="li"
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div className="ms-2 me-auto">
                      <div className="fw-bold">
                        Move & Chmod & Chown Private & Certificate
                      </div>
                      <code>cd /etc/ssl</code>
                      <code>
                        sudo mv ~/ssl-exported/{domain}.crt /etc/ssl/certs/
                      </code>
                      <code>
                        sudo mv ~/ssl-exported/{domain}.key /etc/ssl/private/
                      </code>
                      <code>sudo chmod 600 /etc/ssl/private/{domain}.key</code>
                      <code>
                        sudo chown root:root /etc/ssl/private/{domain}.key
                      </code>
                      <code>sudo chmod 600 /etc/ssl/certs/{domain}.crt</code>
                      <code>
                        sudo chown root:root /etc/ssl/certs/{domain}.crt
                      </code>
                    </div>
                  </ListGroup.Item>
                </ListGroup>
              </Accordion.Body>
            </Accordion.Item>
    )
}

export default SSL;