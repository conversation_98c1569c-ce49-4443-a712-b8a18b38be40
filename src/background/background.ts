// Background script for Ubuntu Webserver Manager
chrome.runtime.onInstalled.addListener(() => {
  console.log('Ubuntu Webserver Manager installed');

  // Enable sidepanel for all tabs (using any to avoid TypeScript issues)
  try {
    (chrome as any).sidePanel?.setPanelBehavior({ openPanelOnActionClick: true });
  } catch (error) {
    console.error('SidePanel API not available:', error);
  }
});

// Handle action click to open sidepanel
chrome.action.onClicked.addListener((tab) => {
  try {
    (chrome as any).sidePanel?.open({ tabId: tab.id });
  } catch (error) {
    console.error('Failed to open sidepanel:', error);
  }
});
