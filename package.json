{"name": "react-chrome-extension", "version": "1.0.0", "description": "react chrome extension template", "scripts": {"start": "webpack --watch --progress --config webpack.dev.js", "build": "webpack --watch --progress --config webpack.prod.js"}, "author": "", "devDependencies": {"@types/chrome": "^0.0.134", "@types/react": "^17.0.3", "@types/react-dom": "^17.0.3", "bootstrap": "^5.2.1", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^7.0.0", "css-loader": "^5.2.4", "html-webpack-plugin": "^4.5.2", "react": "^17.0.2", "react-bootstrap": "^2.5.0", "react-dom": "^17.0.2", "style-loader": "^2.0.0", "terser-webpack-plugin": "^5.1.1", "ts-loader": "^8.1.0", "typescript": "^4.2.4", "webpack": "^5.34.0", "webpack-cli": "^4.6.0", "webpack-merge": "^5.7.3"}}